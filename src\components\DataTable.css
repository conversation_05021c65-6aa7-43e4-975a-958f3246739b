.data-table-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  overflow: hidden;
}

.data-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 5px;
}

.data-table-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.data-table-search {
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.table-responsive {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.data-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #444;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.data-table th.sortable:hover {
  background-color: #eef1f6;
}

.sort-indicator {
  margin-left: 5px;
  font-size: 12px;
}

.data-table tr:hover {
  background-color: #f5f7ff;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid #eee;
  background-color: #f8f9fa;
}

.showing-entries {
  font-size: 14px;
  color: #666;
}

.pagination-container {
  display: flex;
  align-items: center;
}

.pagination-button {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  margin: 0 2px;
  cursor: pointer;
  font-size: 14px;
  color: #444;
  transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.pagination-button.active {
  background-color: #4a6cf7;
  color: white;
  border-color: #4a6cf7;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  margin-left: 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.items-per-page {
  margin-left: 10px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: #f9f9f9;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4a6cf7;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 15px;
  background-color: #fff3f3;
  color: #d32f2f;
  margin: 15px;
  border-radius: 4px;
}

.no-data {
  text-align: center;
  padding: 30px;
  color: #666;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .data-table-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .data-table-search {
    width: 100%;
    margin-top: 10px;
  }
  
  .data-table-footer {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .pagination-container {
    margin-top: 10px;
    width: 100%;
    justify-content: center;
  }
  
  .showing-entries {
    margin-bottom: 10px;
  }
}

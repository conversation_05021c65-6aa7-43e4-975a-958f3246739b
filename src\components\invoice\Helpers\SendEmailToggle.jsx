// SendEmailToggle.jsx
import React from 'react';

const SendEmailToggle = ({ sendEmail, emailUpdateNote, setEmailUpdateNote }) => {
  if (sendEmail) return null; // Don't show anything if email is being sent

  return (
    <div id="note_field" className="form-group mt-3">
      <label htmlFor="email_update_note">Email Update Note:*</label>
      <textarea
        id="email_update_note"
        name="email_update_note"
        rows="4"
        cols="50"
        className="form-control"
        placeholder="Enter your note here..."
        value={emailUpdateNote}
        onChange={(e) => setEmailUpdateNote(e.target.value)}
        required
      />
    </div>
  );
};

export default SendEmailToggle;

/* Modern Dashboard Styles */
.dashboard-container {
  padding: 25px;
  background-color: #f5f7fa;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
}

.dashboard-subtitle {
  font-size: 14px;
  color: #7f8c8d;
}

/* Modern Cards */
.modern-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modern-card-header {
  padding: 20px 25px;
  border-bottom: 1px solid #f1f3f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.modern-card-body {
  padding: 25px;
}

/* Stat Cards */
.stat-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-card-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.stat-card-icon i {
  font-size: 24px;
  color: #fff;
}

.stat-card-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 5px;
  color: #2c3e50;
}

.stat-card-label {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.bg-purple {
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
}

.bg-blue {
  background: linear-gradient(135deg, #1a73e8 0%, #49a3f1 100%);
}

.bg-green {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.bg-orange {
  background: linear-gradient(135deg, #f56036 0%, #fd8d32 100%);
}

/* Chart Section */
.chart-section {
  margin-bottom: 30px;
}

.chart-container {
  height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.chart-placeholder {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  text-align: center;
}

.chart-placeholder img {
  width: 100%;
  max-width: 200px;
  opacity: 0.7;
}

.chart-placeholder-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-top: 20px;
  margin-bottom: 10px;
}

.chart-placeholder-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  max-width: 300px;
  margin: 0 auto;
}

/* Activity Feed */
.activity-feed {
  margin-bottom: 30px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #f1f3f9;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.activity-icon i {
  font-size: 18px;
  color: #fff;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.activity-subtitle {
  font-size: 13px;
  color: #7f8c8d;
  margin: 0;
}

.activity-time {
  font-size: 12px;
  color: #95a5a6;
  white-space: nowrap;
  margin-left: 15px;
}

/* Support Section */
.support-section {
  margin-bottom: 30px;
}

.support-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.support-header-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.support-header-icon i {
  font-size: 20px;
  color: #fff;
}

.support-header-content h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 5px;
}

.support-header-content p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.support-action {
  margin-top: 20px;
}

.support-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 25px;
  background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
  color: #fff;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 5px 15px rgba(106, 17, 203, 0.3);
}

.support-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(106, 17, 203, 0.4);
}

.support-btn i {
  margin-right: 10px;
  font-size: 16px;
}

.ticket-list {
  margin-top: 25px;
}

.ticket-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8fafc;
  border-radius: 12px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.ticket-item:hover {
  background: #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.ticket-priority {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 15px;
}

.priority-high {
  background-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.2);
}

.priority-medium {
  background-color: #f39c12;
  box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.2);
}

.ticket-content {
  flex: 1;
}

.ticket-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.ticket-status {
  font-size: 12px;
  color: #7f8c8d;
}

.ticket-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  margin-left: 10px;
}

.badge-open {
  background-color: #e3f2fd;
  color: #1a73e8;
}

.badge-progress {
  background-color: #fff8e1;
  color: #f39c12;
}

/* Responsive */
@media (max-width: 992px) {
  .stat-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .stat-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-container {
    padding: 15px;
  }
}

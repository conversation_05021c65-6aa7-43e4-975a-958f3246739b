/* Header Logout <PERSON><PERSON> Styles */
.logout-button {
  background-color: transparent;
  color: #6c757d;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  margin-left: 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-button:hover {
  background-color: #f8f9fa;
  color: #dc3545;
}

.logout-button i {
  margin-right: 8px;
  font-size: 16px;
}

.logout-button-icon-only {
  background-color: transparent;
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 15px;
  padding: 0;
}

.logout-button-icon-only:hover {
  transform: scale(1.1);
}

.logout-button-icon-only img {
  width: 32px;
  height: 32px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .logout-button span {
    display: none;
  }

  .logout-button {
    padding: 8px;
  }

  .logout-button i {
    margin-right: 0;
  }
}

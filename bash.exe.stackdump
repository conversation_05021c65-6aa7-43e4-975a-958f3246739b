Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF2D370000 ntdll.dll
7FFF2C630000 KERNEL32.DLL
7FFF2A500000 KERNELBASE.dll
7FFF2C700000 USER32.dll
7FFF2AA20000 win32u.dll
7FFF2C8C0000 GDI32.dll
7FFF2AB20000 gdi32full.dll
7FFF2AA50000 msvcp_win.dll
7FFF2AE70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF2BFF0000 advapi32.dll
7FFF2B110000 msvcrt.dll
7FFF2D020000 sechost.dll
7FFF2AAF0000 bcrypt.dll
7FFF2CCE0000 RPCRT4.dll
7FFF29B80000 CRYPTBASE.DLL
7FFF2AF90000 bcryptPrimitives.dll
7FFF2D290000 IMM32.DLL

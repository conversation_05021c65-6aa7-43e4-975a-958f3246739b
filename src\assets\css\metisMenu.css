/*!
* metismenu https://github.com/onokumus/metismenu#readme
* A jQuery menu plugin
* @version 3.0.6
* <AUTHOR> <<EMAIL>> (https://github.com/onokumus)
* @license: MIT 
*/
.metismenu .arrow {
    float: right;
    line-height: 1.42857;
  }
  *[dir="rtl"] .metismenu .arrow {
    float: left;
  }
  
  /*
   * Require Bootstrap 3.x
   * https://github.com/twbs/bootstrap
  */
  
  .metismenu .glyphicon.arrow:before {
    content: "\e079";
  }
  .metismenu .mm-active > a > .glyphicon.arrow:before {
    content: "\e114";
  }
  
  /*
   * Require Font-Awesome
   * http://fortawesome.github.io/Font-Awesome/
  */
  
  .metismenu .fa.arrow:before {
    content: "\f104";
  }
  .metismenu .mm-active > a > .fa.arrow:before {
    content: "\f107";
  }
  
  /*
   * Require Ionicons
   * http://ionicons.com/
  */
  
  .metismenu .ion.arrow:before {
    content: "\f3d2"
  }
  .metismenu .mm-active > a > .ion.arrow:before {
    content: "\f3d0";
  }
  .metismenu .plus-times {
    float: right;
  }
  *[dir="rtl"] .metismenu .plus-times {
    float: left;
  }
  .metismenu .fa.plus-times:before {
    content: "\f067";
  }
  .metismenu .mm-active > a > .fa.plus-times {
    transform: rotate(45deg);
  }
  .metismenu .plus-minus {
    float: right;
  }
  *[dir="rtl"] .metismenu .plus-minus {
    float: left;
  }
  .metismenu .fa.plus-minus:before {
    content: "\f067";
  }
  .metismenu .mm-active > a > .fa.plus-minus:before {
    content: "\f068";
  }
  .metismenu .mm-collapse:not(.mm-show) {
    display: none;
  }
  
  .metismenu .mm-collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    visibility: hidden;
    transition-timing-function: ease;
    transition-duration: 0.3s;
    transition-property: height, visibility;
  }
  
  .metismenu .wp-has-submenu {
    position: relative;
  }
  
  .metismenu a.wp-has-submenu::after {
    position: absolute;
    content: '';
    /* width: .5em;
    height: .5em; */
    /* border-width: 1px 0 0 1px; */
    /* border-style: solid;
    border-color: currentColor;
    border-color: initial;
    right: 1em; */
    transform: rotate(-90deg) translate(0, -50%);
    transform-origin: top;
    top: 50%;
    transition: all .3s ease-out;
    right: 40px;
     content: "\e64b";
     font-family: 'themify';
      color: #A5ADC6;
      font-weight: 600;
      font-size: 14px;

  }
  
  *[dir="rtl"] .metismenu .wp-has-submenu::after {
    right: auto;
    left: 1em;
    transform: rotate(180deg) translate(0, -50%);
  }
  
  .metismenu .mm-active > .wp-has-submenu::after,
  .metismenu .wp-has-submenu[aria-expanded="true"]::after {
    transform: rotate(180deg) translate(0, -50%);
  }
  
  *[dir="rtl"] .metismenu .mm-active > .wp-has-submenu::after,
  *[dir="rtl"] .metismenu .wp-has-submenu[aria-expanded="true"]::after {
    transform: rotate(180deg) translate(0, -50%);
  }
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API</title>
</head>
<body>
    <h1>Test API</h1>
    <button id="testProxyButton">Test Proxy API</button>
    <button id="testDirectButton">Test Direct API</button>
    <pre id="result"></pre>

    <script>
        document.getElementById('testProxyButton').addEventListener('click', async () => {
            const resultElement = document.getElementById('result');
            resultElement.textContent = 'Loading...';

            try {
                const response = await fetch('http://localhost:3002/products-api/get-project-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ project_id: '1700' }),
                });

                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
            }
        });

        document.getElementById('testDirectButton').addEventListener('click', async () => {
            const resultElement = document.getElementById('result');
            resultElement.textContent = 'Loading...';

            try {
                const response = await fetch('https://portal.occamsadvisory.com/portal/wp-json/productsplugin/v1/get-project-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ project_id: '1700' }),
                });

                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>

/* Custom styles for the contact dropdown in Link Contact modal */

/* Increase the width of the dropdown menu */
.contact-select-container .react-select__menu {
  width: 100%;
  max-height: 250px;
  overflow-y: auto;
  z-index: 9999 !important;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Style the dropdown options */
.contact-select-container .react-select__option {
  padding: 10px 15px;
  cursor: pointer;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
}

/* Style the selected option */
.contact-select-container .react-select__option--is-selected {
  background-color: #f7941d;
  color: white;
}

/* Style the focused option */
.contact-select-container .react-select__option--is-focused:not(.react-select__option--is-selected) {
  background-color: #fff3e0;
  color: #333;
}

/* Style the dropdown control */
.contact-select-container .react-select__control {
  min-height: 40px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  box-shadow: none;
}

/* Style the dropdown control when focused */
.contact-select-container .react-select__control--is-focused {
  border-color: #f7941d;
  box-shadow: 0 0 0 1px #f7941d;
}

/* Style the dropdown value container */
.contact-select-container .react-select__value-container {
  padding: 2px 8px;
}

/* Style the dropdown placeholder */
.contact-select-container .react-select__placeholder {
  color: #6c757d;
}

/* Style the dropdown indicators */
.contact-select-container .react-select__indicators {
  cursor: pointer;
}

/* Hide the dropdown indicator separator */
.contact-select-container .react-select__indicator-separator {
  display: none;
}

/* Style the dropdown clear indicator */
.contact-select-container .react-select__clear-indicator {
  color: #6c757d;
}

/* Hide the dropdown indicator */
.contact-select-container .react-select__dropdown-indicator {
  display: none;
}

/* Style the dropdown loading indicator */
.contact-select-container .react-select__loading-indicator {
  color: #f7941d;
}

/* Style the dropdown multi-value */
.contact-select-container .react-select__multi-value {
  background-color: #fff3e0;
  border-radius: 2px;
}

/* Style the dropdown multi-value label */
.contact-select-container .react-select__multi-value__label {
  color: #333;
  font-size: 85%;
}

/* Style the dropdown multi-value remove */
.contact-select-container .react-select__multi-value__remove {
  color: #6c757d;
  cursor: pointer;
}

/* Style the dropdown multi-value remove on hover */
.contact-select-container .react-select__multi-value__remove:hover {
  background-color: #f7941d;
  color: white;
}

/* Style the dropdown menu portal */
.contact-select-container .react-select__menu-portal {
  z-index: 9999;
}

/* Style the dropdown group */
.contact-select-container .react-select__group {
  padding: 0;
}

/* Style the dropdown group heading */
.contact-select-container .react-select__group-heading {
  color: #6c757d;
  font-weight: 600;
  font-size: 85%;
  margin-bottom: 0.25em;
  padding: 5px 10px;
}

/* Style the no options message */
.contact-select-container .react-select__menu-notice--no-options {
  padding: 10px;
  text-align: center;
  color: #6c757d;
}

/* Style the loading message */
.contact-select-container .react-select__menu-notice--loading {
  padding: 10px;
  text-align: center;
  color: #6c757d;
}

/* Style the contact option */
.contact-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Style the contact name */
.contact-option .contact-name {
  font-weight: 500;
  color: #333;
}

/* Style the contact ID */
.contact-option .contact-id {
  color: #6c757d;
  font-size: 90%;
}

/* Fix for dropdown portal positioning */
.react-select__menu-portal {
  z-index: 1070 !important; /* Higher than modal (1060) */
}

/* Ensure dropdown appears above modal */
body .react-select__menu {
  z-index: 1070 !important; /* Higher than modal (1060) */
}

/* Modal backdrop and modal styles */
.modal-backdrop {
  opacity: 0.5;
}

/* Center the modal dialog */
.modal-dialog {
  max-width: 500px;
  margin: 1.75rem auto;
}

/* Improve modal content styling */
.modal-content {
  border: none;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Style the modal header */
.modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 1rem 1.5rem;
}

.modal-header .modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.modal-header .btn-close {
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
  opacity: 0.5;
  transition: opacity 0.2s;
}

.modal-header .btn-close:hover {
  opacity: 0.75;
}

/* Style the modal body */
.modal-body {
  padding: 1.5rem;
}

/* Style the modal footer */
.modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 1rem 1.5rem;
  justify-content: space-between;
}

/* Fix for Select component in modal */
.contact-select-container {
  position: relative;
  z-index: 1;
}

/* Additional styles for the modal */
.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 3.5rem);
}

/* Fix for modal content */
.modal-content {
  width: 100%;
}

/* Fix for Select component in modal */
.contact-select-container .react-select__control {
  background-color: #fff;
}

/* Fix for dropdown in modal */
.contact-select-container .react-select__menu {
  position: absolute;
  margin-top: 4px;
  width: 100%;
}

/* Contact card styles */
.contact-card .card-exam {
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.contact-card .card-exam:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

/* Circle avatar styles */
.contact-card .circle {
  background-color: #f7941d;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

.contact-card:hover .circle {
  transform: scale(1.05);
}

/* Contact card title styles */
.contact-card .card-exam-title p {
  margin-bottom: 5px;
}

.contact-card .card-exam-title a {
  color: #333;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
}

.contact-card .card-exam-title a:hover {
  color: #f7941d;
}

/* Animation for new contact cards */
@keyframes highlightNew {
  0% {
    box-shadow: 0 0 0 rgba(247, 148, 29, 0);
  }
  50% {
    box-shadow: 0 0 20px rgba(247, 148, 29, 0.8);
  }
  100% {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.contact-card.new-contact .card-exam {
  animation: highlightNew 2s ease-in-out;
}

/* Loading spinner styles */
.contact_tab_data .spinner-border {
  color: #f7941d !important;
  width: 3rem;
  height: 3rem;
}

.contact_tab_data .text-center p {
  color: #666;
  font-size: 1rem;
  margin-top: 1rem;
}

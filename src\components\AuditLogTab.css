.audit-log-tab {
  padding: 20px;
}

.audit-log-tab h3 {
  margin-bottom: 20px;
  color: #333;
}

.audit-log-table-container {
  overflow-x: auto;
  margin-top: 20px;
}

.audit-log-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.audit-log-table th,
.audit-log-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.audit-log-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #495057;
}

.audit-log-table tr:hover {
  background-color: #f5f5f5;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 20px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  margin-top: 20px;
  text-align: center;
}

.error-message button {
  margin-top: 10px;
}

.no-logs-message {
  padding: 20px;
  background-color: #e2e3e5;
  color: #383d41;
  border-radius: 4px;
  margin-top: 20px;
  text-align: center;
}

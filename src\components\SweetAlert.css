/* Custom styles for SweetAlert2 popups */
.swal2-popup {
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  font-family: 'Mulish', sans-serif;
}

.swal2-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.swal2-html-container {
  font-size: 16px;
  color: #555;
  margin-bottom: 20px;
}

.swal2-icon {
  margin: 15px auto;
}

.swal2-confirm {
  padding: 10px 24px;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.swal2-confirm:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.swal2-confirm:focus {
  box-shadow: 0 0 0 3px rgba(255, 127, 80, 0.3);
}

/* Styled confirm button - Orange for Save Note */
.swal2-styled.swal2-confirm {
  background-color: #FF6B00 !important; /* Orange color */
  color: #fff;
  border: none;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 24px;
  margin: 5px 15px; /* Increased horizontal margin for more spacing */
  border-radius: 6px; /* Slightly more rounded corners */
  display: inline-block;
  min-width: 120px; /* Minimum width */
}

.swal2-styled.swal2-confirm:hover {
  background-color: #E86000 !important; /* Slightly darker orange on hover */
  box-shadow: 0 4px 8px rgba(255, 107, 0, 0.3);
}

/* Styled cancel button - Red */
.swal2-styled.swal2-cancel {
  background-color: #6c757d !important; /* Gray color instead of red */
  color: #fff;
  border: none;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 24px;
  margin: 5px 15px; /* Increased horizontal margin for more spacing */
  border-radius: 6px; /* Slightly more rounded corners */
  display: inline-block;
  min-width: 120px; /* Minimum width */
}

.swal2-styled.swal2-cancel:hover {
  background-color: #5a6268 !important; /* Slightly darker gray on hover */
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Button container spacing */
.swal2-actions {
  margin-top: 20px !important;
  gap: 15px !important;
  justify-content: center !important;
}

/* Custom actions class for more spacing */
.swal2-actions-custom {
  display: flex !important;
  gap: 30px !important; /* Even more spacing between buttons */
  justify-content: center !important;
}

/* Custom classes */
.swal-custom-popup {
  width: 400px;
  max-width: 90%;
}

.swal-custom-title {
  font-size: 20px;
}

.swal-custom-confirm-button {
  font-size: 15px;
}

/* Animation */
.swal2-show {
  animation: swal2-show 0.3s;
}

.swal2-hide {
  animation: swal2-hide 0.3s;
}

@keyframes swal2-show {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes swal2-hide {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.9);
    opacity: 0;
  }
}

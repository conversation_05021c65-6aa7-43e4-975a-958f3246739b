/* ContactCard Component Styles */

.contact-card-component {
  display: inline-block;
}

.contact-card-content {
  padding: 0.5rem;
}

.contact-card-content h5 {
      font-size: 15px;
}

.contact-card .card-exam {
  padding: 10px 10px 10px 10px;
  height: 100%;
}

.contact-card .circle {
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50% !important;
  -moz-border-radius: 50% !important;
  -webkit-border-radius: 50% !important;
  text-align: center;
  color: #0f67ae;
  font-size: 20px;
  text-transform: uppercase;
  font-weight: 700;
  background: #f3f0f2;
  border: 1px solid rgb(231 231 231 / 70%);
  top: 0;
  margin-right: 20px;
  overflow: inherit !important;
}

.contact-card .card-exam-title {
  text-align: left;
  width: 80%;
}

.contact-card .card-exam-title p {
  font-size: 15px !important;
  color: #000 !important;
  font-weight: 500 !important;
  word-break: break-all;
  line-height: 27px;
  margin-bottom: 0;
  margin-top: 0;
}

.contact-card .card-exam-title p:first-child {
  color: #0f67ae !important;
  font-weight: 600 !important;
}

/* Badge styles */
.contact-card-content .badge {
  font-size: 0.75rem;
  padding: 0.35em 0.65em;
}

/* Header Dropdown Menu Styles */

/* Top secondary menu */
#wp-admin-bar-top-secondary {
  float: right;
}

/* Menu popup styles */
.menupop {
  position: relative;
}

.menupop .ab-item {
  cursor: pointer;
  color: #333;
  text-decoration: none;
  padding: 8px 15px;
  display: block;
}

/* Submenu wrapper */
.ab-sub-wrapper {
  position: absolute;
  top: 100%;
  right: 0;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  min-width: 200px;
  z-index: 1000;
  display: none;
}

/* Profile info styles */
.profile_info_iner {
  padding: 15px;
  background-color: #fff;
  border-radius: 0 0 10px 10px;
  box-shadow: 0px 10px 20px rgba(108, 39, 255, 0.3);
}

.profile_info_details {
  padding: 0;
}

/* Submenu items */
.ab-submenu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ab-submenu li {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
}

.ab-submenu li:last-child {
  border-bottom: none;
}

.ab-submenu .ab-item {
  padding: 10px 15px;
  display: block;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
}

.ab-submenu .ab-item:hover {
  background-color: #f5f5f5;
}

/* Avatar and display name styling */
.with-avatar .ab-item {
  display: flex;
  align-items: center;
}

.display-name {
  font-weight: 500;
  margin-left: 5px;
}

/* Header bar styling */
#wpadminbar {
  position: relative;
  display: inline-block;
  z-index: 1; /* Ensure header is above most elements but below modals */
}

/* Ensure profile info has proper z-index */
.profile_info {
  z-index: 1051; /* Higher than header bar */
}

/* Profile name display should be above notifications */
.profile_info .ab-item {
  position: relative;
  z-index: 1052;
}

/* Custom SweetAlert2 toast positioning to avoid header overlap */
.swal-toast-container-custom {
  top: 80px !important; /* Position below header */
  z-index: 1049 !important; /* Below header but above other content, below modals */
}

/* Ensure SweetAlert2 toasts don't overlap with header on mobile */
@media (max-width: 768px) {
  .swal-toast-container-custom {
    top: 70px !important;
    right: 10px !important;
  }
}


.modal-backdrop {
    opacity: 1 !important;
}